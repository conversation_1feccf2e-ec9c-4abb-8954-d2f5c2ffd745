<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔴 LIVE Stream - JW Player</title>

    <!-- JW Player CSS & JS -->
    <script src="https://cdn.jwplayer.com/libraries/KB5zFt7A.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #1a1a2e;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #fff;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        .livestream-container {
            max-width: 1400px;
            margin: 0 auto;
            background: #1a1a2e;
            min-height: 100vh;
            padding: 0 20px;
        }

        @media (max-width: 768px) {
            .livestream-container {
                padding: 0 10px;
            }
        }

        .livestream-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px 0;
            margin: 0 -20px 20px -20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .header-content {
            max-width: 1400px;
            width: 100%;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        @media (max-width: 768px) {
            .livestream-header {
                margin: 0 -10px 15px -10px;
            }

            .header-content {
                padding: 0 10px;
            }
        }

        .livestream-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .live-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
            z-index: 1;
        }

        .live-dot {
            width: 14px;
            height: 14px;
            background: #ff4757;
            border-radius: 50%;
            animation: pulse 1.5s infinite;
            box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); box-shadow: 0 0 10px rgba(255, 71, 87, 0.5); }
            50% { opacity: 0.8; transform: scale(1.2); box-shadow: 0 0 20px rgba(255, 71, 87, 0.8); }
            100% { opacity: 1; transform: scale(1); box-shadow: 0 0 10px rgba(255, 71, 87, 0.5); }
        }

        .live-text {
            font-weight: bold;
            font-size: 1.3em;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .viewer-count {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 18px;
            border-radius: 25px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1;
        }

        .player-wrapper {
            position: relative;
            background: #000;
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            aspect-ratio: 16/9;
            min-height: 350px;
            max-height: 65vh;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
        }

        #jwplayer-container {
            width: 100%;
            height: 100%;
            border-radius: 12px;
            overflow: hidden;
        }

        /* Force clean video display */
        #jwplayer-container .jw-media {
            border-radius: 12px;
        }

        /* Responsive cho mobile */
        @media (max-width: 768px) {
            .player-wrapper {
                max-width: calc(100% - 20px);
                margin: 0 10px;
                min-height: 220px;
                max-height: 45vh;
                border-radius: 8px;
            }
        }

        @media (max-width: 480px) {
            .player-wrapper {
                margin: 0 5px;
                min-height: 180px;
                max-height: 35vh;
                border-radius: 6px;
            }
        }

        /* Hide ALL JW Player UI Elements */
        .jw-controls,
        .jw-controlbar,
        .jw-display,
        .jw-display-icon-container,
        .jw-settings-menu,
        .jw-rightclick,
        .jw-overlays,
        .jw-nextup-container,
        .jw-logo,
        .jw-title,
        .jw-captions,
        .jw-preview,
        .jw-text-elapsed,
        .jw-text-duration,
        .jw-slider-time,
        .jw-button-container,
        .jw-spacer,
        .jw-group {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }

        /* Custom Live Overlay */
        .live-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 71, 87, 0.95);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1em;
            z-index: 2000;
            display: flex;
            align-items: center;
            gap: 10px;
            backdrop-filter: blur(15px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .live-overlay .dot {
            width: 10px;
            height: 10px;
            background: white;
            border-radius: 50%;
            animation: livePulse 1.5s infinite;
        }

        @keyframes livePulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* Hide JW Player UI completely */
        .jwplayer .jw-controls {
            display: none !important;
        }

        .jwplayer .jw-display {
            display: none !important;
        }

        .jwplayer .jw-logo {
            display: none !important;
        }

        .jwplayer .jw-media {
            cursor: default !important;
        }

        /* Unmute Popup Styles */
        .unmute-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(26, 26, 46, 0.98);
            color: white;
            padding: 40px;
            border-radius: 25px;
            text-align: center;
            z-index: 10000;
            backdrop-filter: blur(20px);
            border: 3px solid #667eea;
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.4);
            animation: popupFadeIn 0.5s ease-out;
            max-width: 450px;
            width: 90%;
        }

        @keyframes popupFadeIn {
            from { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }

        .unmute-popup h3 {
            margin-bottom: 25px;
            color: #667eea;
            font-size: 1.8em;
            text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .unmute-popup p {
            margin-bottom: 30px;
            font-size: 1.2em;
            line-height: 1.7;
            color: #e8e8e8;
        }

        .chrome-info {
            background: rgba(102, 126, 234, 0.15);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 25px;
            font-size: 1em;
            color: #a8b3ff;
            line-height: 1.5;
        }

        .unmute-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 35px;
            border-radius: 30px;
            font-size: 1.2em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .unmute-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.5);
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
        }

        .livestream-info {
            background: #1a1a2e;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
            max-width: 1200px;
            margin: 20px auto 0 auto;
        }

        .stream-details {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .stream-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #fff;
        }

        .stream-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
        }

        .chat-panel {
            background: #16213e;
            border-radius: 10px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
        }

        .chat-header {
            font-weight: bold;
            margin-bottom: 15px;
            color: #9146ff;
            border-bottom: 1px solid rgba(145, 70, 255, 0.3);
            padding-bottom: 10px;
        }

        .chat-message {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            font-size: 0.9em;
        }

        .chat-user {
            color: #9146ff;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .livestream-info {
                grid-template-columns: 1fr;
            }

            .livestream-header {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .player-wrapper {
                height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="livestream-container">
        <!-- Livestream Header -->
        <div class="livestream-header">
            <div class="header-content">
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span class="live-text">🔴 LIVE</span>
                </div>
                <div class="viewer-count">
                    <i class="fas fa-eye"></i>
                    <span id="viewer-count">1,247</span> người xem
                </div>
            </div>
        </div>

        <!-- Video Player -->
        <div class="player-wrapper">
            <div id="jwplayer-container"></div>
            <div class="live-overlay">
                <div class="dot"></div>
                LIVE
            </div>
        </div>

        <!-- Livestream Info -->
        <div class="livestream-info">
            <div class="stream-details">
                <div class="stream-title">🎬 Gói Giải Pháp - 21 Ngày Giảm Cân-Giảm Mỡ Chuẩn Y Khoa</div>
                <div class="stream-stats">
                    <div class="stat-item">
                        <i class="fas fa-signal"></i>
                        <span id="quality">1080p</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span id="duration">Tự động phát từ 60s</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-volume-up"></i>
                        <span id="audio-status">Đã tắt tiếng</span>
                    </div>
                </div>
            </div>

            <div class="chat-panel">
                <div class="chat-header">💬 Chat trực tiếp</div>
                <div id="chat-messages">
                    <div class="chat-message">
                        <span class="chat-user">Viewer123:</span> Chất lượng stream tuyệt vời! 👍
                    </div>
                    <div class="chat-message">
                        <span class="chat-user">StreamFan:</span> Âm thanh rất hay 🎵
                    </div>
                    <div class="chat-message">
                        <span class="chat-user">MovieLover:</span> Phim này hay quá! 🎬
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Unmute Popup -->
    <div id="unmute-popup" class="unmute-popup" style="display: none;">
        <h3><i class="fas fa-volume-up"></i> Bật âm thanh</h3>
        <div class="chrome-info">
            <i class="fab fa-chrome"></i> Trình duyệt đã tự động tắt âm để cho phép phát video từ 60 giây
        </div>
        <p>Nhấn nút bên dưới để bật âm thanh và thưởng thức buổi livestream về giảm cân chuẩn y khoa!</p>
        <button class="unmute-btn" onclick="unmuteVideo()">
            <i class="fas fa-volume-up"></i>
            Bật âm thanh ngay
        </button>
    </div>

    <script>
        // JW Player License Key
        jwplayer.key = "YgtWotBOi+JsQi+stgRlQ3SK21W2vbKi/K2V86kVbwU=";

        // Global variables
        let viewerCount = 1247;
        let chatMessages = [];
        let player;

        // URL M3U8 stream
        const streamUrl = 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8';

        // Khởi tạo JW Player cho livestream
        function initPlayer() {
            player = jwplayer('jwplayer-container').setup({
                file: streamUrl,
                width: '100%',
                height: '100%',
                autostart: true,
                mute: true,
                controls: false,
                displaytitle: false,
                displaydescription: false,
                abouttext: "",
                aboutlink: "",
                repeat: true,
                stretching: "fill",
                logo: {
                    hide: true
                },
                related: {
                    displayMode: "none"
                },
                sharing: {
                    sites: []
                },
                skin: {
                    name: "seven",
                    active: "#FF0000",
                    inactive: "rgba(255,255,255,0.8)",
                    background: "transparent"
                }
            });

            // Ẩn hoàn toàn UI để giả lập live
            player.on('ready', function() {
                console.log('JW Player sẵn sàng cho livestream');

                // Ẩn tất cả controls và UI
                const jwContainer = document.querySelector('#jwplayer-container');
                if (jwContainer) {
                    // Thêm CSS để ẩn hoàn toàn UI
                    const style = document.createElement('style');
                    style.textContent = `
                        #jwplayer-container .jw-controls,
                        #jwplayer-container .jw-controlbar,
                        #jwplayer-container .jw-display,
                        #jwplayer-container .jw-logo,
                        #jwplayer-container .jw-title,
                        #jwplayer-container .jw-overlays,
                        #jwplayer-container .jw-nextup-container {
                            display: none !important;
                            visibility: hidden !important;
                        }

                        #jwplayer-container .jw-media {
                            cursor: default !important;
                        }

                        #jwplayer-container:hover .jw-controls {
                            display: none !important;
                        }
                    `;
                    document.head.appendChild(style);
                }
            });

            // Seek đến 60 giây khi có thể phát
            player.on('firstFrame', function() {
                console.log('Video bắt đầu phát');

                // Seek đến 60 giây
                player.seek(400);

                // Hiển thị popup unmute sau 3 giây
                setTimeout(() => {
                    showUnmutePopup();
                }, 3000);

                // Đảm bảo ẩn controls sau khi video bắt đầu
                setTimeout(() => {
                    hideAllControls();
                }, 1000);
            });

            // Function để ẩn tất cả controls
            function hideAllControls() {
                const elements = document.querySelectorAll(`
                    #jwplayer-container .jw-controls,
                    #jwplayer-container .jw-controlbar,
                    #jwplayer-container .jw-display,
                    #jwplayer-container .jw-logo,
                    #jwplayer-container .jw-title,
                    #jwplayer-container .jw-overlays,
                    #jwplayer-container .jw-nextup-container,
                    #jwplayer-container .jw-display-icon-container
                `);

                elements.forEach(el => {
                    if (el) {
                        el.style.display = 'none';
                        el.style.visibility = 'hidden';
                        el.style.opacity = '0';
                    }
                });
            }

            // Xử lý các sự kiện khác
            player.on('play', function() {
                console.log('Livestream đang phát');
                document.getElementById('duration').textContent = 'Đang phát trực tiếp';
            });

            player.on('pause', function() {
                console.log('Livestream đã tạm dừng');
                document.getElementById('duration').textContent = 'Đã tạm dừng';
            });

            player.on('mute', function(event) {
                const audioStatus = document.getElementById('audio-status');
                if (event.mute) {
                    audioStatus.textContent = 'Đã tắt tiếng';
                } else {
                    audioStatus.textContent = `Âm lượng: ${Math.round(player.getVolume())}%`;
                }
            });

            player.on('volume', function(event) {
                if (!player.getMute()) {
                    document.getElementById('audio-status').textContent = `Âm lượng: ${Math.round(event.volume)}%`;
                }
            });

            player.on('error', function(event) {
                console.error('Lỗi JW Player:', event);
                // Thử tải lại sau 3 giây
                setTimeout(() => {
                    console.log('Đang thử kết nối lại livestream...');
                    player.load([{
                        file: streamUrl
                    }]);
                }, 3000);
            });
        }

        // Function to show unmute popup
        function showUnmutePopup() {
            document.getElementById('unmute-popup').style.display = 'block';
        }

        // Function to unmute video
        function unmuteVideo() {
            player.setMute(false);
            player.setVolume(80);
            document.getElementById('unmute-popup').style.display = 'none';
            document.getElementById('audio-status').textContent = 'Đã bật tiếng';

            // Thêm hiệu ứng thông báo
            const audioStatus = document.getElementById('audio-status');
            audioStatus.style.background = '#667eea';
            audioStatus.style.color = 'white';
            audioStatus.style.padding = '5px 10px';
            audioStatus.style.borderRadius = '15px';
            setTimeout(() => {
                audioStatus.style.background = '';
                audioStatus.style.color = '';
                audioStatus.style.padding = '';
                audioStatus.style.borderRadius = '';
            }, 3000);
        }

        // Function to update viewer count
        function updateViewerCount() {
            viewerCount += Math.floor(Math.random() * 10) - 5;
            if (viewerCount < 1000) viewerCount = 1000 + Math.floor(Math.random() * 500);
            document.getElementById('viewer-count').textContent = viewerCount.toLocaleString();
        }

        // Function to add chat message
        function addChatMessage(user, message) {
            const chatContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message';
            messageDiv.innerHTML = `<span class="chat-user">${user}:</span> ${message}`;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            // Remove old messages if too many
            if (chatContainer.children.length > 10) {
                chatContainer.removeChild(chatContainer.firstChild);
            }
        }

        // Simulate live chat
        const chatUsers = ['StreamFan', 'MovieLover', 'Viewer123', 'LiveWatcher', 'VideoFan', 'CinemaLover'];
        const chatTexts = [
            'Chất lượng tuyệt vời! 👍',
            'Âm thanh rất hay 🎵',
            'Stream mượt quá! 🔥',
            'Cảm ơn streamer! ❤️',
            'Phim này hay lắm 🎬',
            'Quality 1080p đỉnh! 💯',
            'Không lag gì cả 👌',
            'Tiếp tục nhé! 🚀'
        ];

        function simulateChat() {
            const randomUser = chatUsers[Math.floor(Math.random() * chatUsers.length)];
            const randomText = chatTexts[Math.floor(Math.random() * chatTexts.length)];
            addChatMessage(randomUser, randomText);
        }

        // Start intervals for live simulation
        setInterval(updateViewerCount, 5000); // Update viewer count every 5 seconds
        setInterval(simulateChat, 3000); // Add chat message every 3 seconds

        // Khởi tạo player khi DOM ready
        document.addEventListener('DOMContentLoaded', function() {
            initPlayer();
        });

        // Custom controls cho livestream (chỉ âm thanh)
        document.addEventListener('keydown', (e) => {
            if (e.target.tagName.toLowerCase() === 'input') return;
            if (!player) return;

            switch(e.code) {
                case 'ArrowUp':
                    e.preventDefault();
                    const currentVol = player.getVolume();
                    player.setVolume(Math.min(100, currentVol + 10));
                    if (player.getMute()) {
                        player.setMute(false);
                    }
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    const vol = player.getVolume();
                    player.setVolume(Math.max(0, vol - 10));
                    break;
                case 'KeyF':
                    e.preventDefault();
                    player.setFullscreen(!player.getFullscreen());
                    break;
                case 'KeyM':
                case 'KeyU':
                    e.preventDefault();
                    if (player.getMute()) {
                        unmuteVideo();
                    } else {
                        player.setMute(true);
                        document.getElementById('audio-status').textContent = 'Đã tắt tiếng';
                    }
                    break;
            }
        });

        // Ngăn click vào video hiển thị controls
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const jwContainer = document.querySelector('#jwplayer-container');
                if (jwContainer) {
                    jwContainer.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        // Chỉ cho phép unmute nếu đang muted
                        if (player && player.getMute()) {
                            showUnmutePopup();
                        }
                    });
                }
            }, 2000);
        });

        // Click outside popup to close
        document.addEventListener('click', (e) => {
            const popup = document.getElementById('unmute-popup');
            if (e.target === popup) {
                popup.style.display = 'none';
            }
        });

        // Cleanup khi trang được đóng
        window.addEventListener('beforeunload', () => {
            if (player) {
                player.remove();
            }
        });
    </script>
</body>
</html>